'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function HeicToPng() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');

    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/heic-to-png');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/heic-to-png');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);

    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.heic') && !lowerName.endsWith('.heif');
    });

    if (invalidFiles.length > 0) {
      setError('Please only upload HEIC/HEIF files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/heic': ['.heic', '.HEIC'],
      'image/heif': ['.heif', '.HEIF']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload HEIC/HEIF files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });

    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev =>
          prev.map(f =>
            f.id === fileItem.id
              ? { ...f, status: 'converting' }
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/heic-to-png', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'done', downloadUrl: result.url }
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'error', errorMessage: errorMessage }
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.heic', '').replace('.HEIC', '').replace('.heif', '').replace('.HEIF', '') + '.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free HEIC to PNG Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert HEIC photos to standard PNG format online
        </p>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="mb-6 md:mb-8 relative">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive
                ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80'
                : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
              } shadow-sm hover:shadow-md`}
          >
            <input {...getInputProps()} accept=".heic,.HEIC,.heif,.HEIF" />
            <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
            <p className="text-lg md:text-xl mb-2 text-gray-800">Select HEIC Photos</p>
            <p className="text-sm text-gray-600 mb-2">from your device</p>
            <p className="text-xs text-gray-500">Supports up to 100 files</p>

            {isMobile && (
              <div className="mt-4 text-sm text-indigo-600 font-medium">
                Tap here to select photos from your device
              </div>
            )}
          </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
        </div>

        {files.length > 0 && (
          <div className="space-y-4 md:space-y-6">
            <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
              <table className="min-w-full table-fixed">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {files.map((file, index) => (
                    <tr key={file.id}>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                      <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                        <div className="truncate" title={file.name}>
                          {isMobile ?
                            file.name.length > 10 ?
                              file.name.slice(0, 7) + '...' + file.name.slice(-3)
                              : file.name
                            : file.name
                          }
                        </div>
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                        {file.status === 'done' && file.downloadUrl ? (
                          <a
                            href={file.downloadUrl}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            download
                          >
                            Download
                          </a>
                        ) : file.status === 'error' ? (
                          <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                        ) : file.status === 'converting' ? (
                          <span className="text-yellow-500 text-sm">Converting...</span>
                        ) : (
                          <span className="text-gray-500 text-sm">Waiting</span>
                        )}
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                        {file.status === 'done' ? (
                          <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                        ) : (
                          <button
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                            disabled={file.status === 'converting'}
                            title="Delete File"
                          >
                            <FiX className="w-5 h-5" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
              <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                  <select
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value="PNG"
                    disabled
                  >
                    <option>PNG</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={quality}
                      onChange={(e) => setQuality(parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-600 w-12">{quality}%</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={removeExif}
                    onChange={(e) => setRemoveExif(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Remove all EXIF information</span>
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={consentPrivacy}
                  onChange={(e) => setConsentPrivacy(e.target.checked)}
                  className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  required
                />
                <label className="text-sm text-gray-600">
                  I consent to heic-tojpg.com collecting and processing my data according to{' '}
                  <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                    Privacy Policy
                  </Link>
                  .
                </label>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                <button
                  onClick={handleClearAll}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                  disabled={isConverting}
                >
                  Clear all
                </button>
                <button
                  onClick={handleConvert}
                  disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isConverting ? 'Converting...' : 'Convert'}
                </button>

                <button
                  onClick={handleDownloadAll}
                  disabled={!files.some(f => f.status === 'done')}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                >
                  <FiDownload className="w-4 h-4" />
                  <span>Download all</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Share buttons */}
        <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
          <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
          <div className="sharethis-inline-share-buttons"></div>
        </div>
        {/* Related tools */}
        <RelatedTools currentTool="HEIC to PNG" />

        {/* Conversion progress indicator */}
        {isConverting && isMobile && (
          <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
            <div className="text-center text-sm text-indigo-800 font-medium">
              Converting: {convertProgress.current} of {convertProgress.total} files
            </div>
            <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300"
                style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
              />
            </div>
          </div>
        )}

        <div className="mt-12 space-y-16">
          <section className="introduction">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              <a href="/heic-to-png" className="hover:text-indigo-600 transition-colors">HEIC to PNG</a> Converter Features
            </h2>
            <div className="space-y-16">
              {/* Feature Group 1: Free & Easy + Fast Conversion */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                      Professional HEIC to PNG Conversion - 100% Free
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>No registration or payment required - completely free tool to convert .HEIC to .PNG with enterprise-grade quality</li>
                      <li>Intuitive drag-and-drop interface with one-click upload functionality for efficient batch processing of HEIC files</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                      High-Speed HEIC to PNG Conversion with Lossless Quality
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Using advanced HEIF codec interpretation and alpha channel preservation for pixel-perfect HEIC file conversion</li>
                      <li>Maintains 32-bit color depth with HVCX to PNG transformation ensuring maximum color accuracy in your images</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/heic-to-png-converter-tool.webp"
                    alt="Professional HEIC to PNG Converter Tool"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 2: Security & Privacy */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg"
                    alt="Online Convert HEIC to PNG Format Converter"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                      Watermark-Free & Unlimited HEIC to PNG Converter
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Converted PNG files are completely watermark-free, ready for professional photo editing after converting HEIC to PNG</li>
                      <li>No file size limits, no quantity restrictions - convert .HEIC to PNG anytime, anywhere with our advanced converter</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                      HEIC to PNG – End-to-End Encryption & Privacy Protection
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Employing AES-256 bit encryption standards to ensure HEIC file security during transmission and processing</li>
                      <li>Using convert-and-delete technology - files are immediately removed from servers after converting HEIC to PNG</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Feature Group 3: Batch Processing & Compatibility */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                      Efficient Batch HEIC to PNG Conversion Technology
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Multi-threaded processing technology to simultaneously convert multiple HEIC files to PNG format</li>
                      <li>Perfect for iPhone and iPad users who need to convert .HEIC to .PNG for cross-platform compatibility</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                      Cross-Platform HEIC to PNG Conversion Compatibility
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>HEIC to PNG converter supports all major browsers including Chrome, Firefox, Edge, Safari and more</li>
                      <li>Compatible with Windows, macOS, Linux, Android, and iOS devices - a truly universal HEIC to PNG converter</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg"
                    alt="Batch HEIC to PNG Conversion Compatibility"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 4: Quality Control & Online Access */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg"
                    alt="Online HEIC to PNG Quality Control"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                      Professional HEIC to PNG Converter with Advanced Image Processing
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Optimized PNG compression parameters, balancing image quality and file size when converting HEIC to PNG</li>
                      <li>Supports ICC color profile management and gamma correction for precise color reproduction in your converted PNG images</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                      Cloud-Based HEIC to PNG Conversion - No Software Installation Required
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Pure cloud processing - no software or plugins needed to convert .HEIC to PNG files from any device</li>
                      <li>WebAssembly optimization technology for efficient browser-based HEIC image processing with HEVC codec support</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="what-is">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the HEIC to PNG Converter</h2>

            <div className="space-y-16">
              {/* HEIC Introduction Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is HEIC to PNG Conversion?</h3>
                    <p className="text-gray-600">
                      HEIC to PNG conversion is the process of transforming Apple's High-Efficiency Image Container format into the universally compatible PNG format. While HEIC offers excellent compression using HEVC (H.265) codec technology,
                      not all software and platforms support it, making PNG a more widely compatible choice. Our HEIC to PNG converter ensures full preservation of image quality during the conversion process.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is the HEIC Format?</h3>
                    <p className="text-gray-600">
                      HEIC (High-Efficiency Image Container) is Apple's implementation of the HEIF (High-Efficiency Image File) format that provides superior compression for photos while maintaining high quality. It uses HEVC (H.265) video codec for compression,
                      achieving file sizes up to 50% smaller than comparable JPEG files. Despite these advantages, many design applications and older systems still require PNG format, necessitating a reliable HEIC to PNG converter. You can visit: <a href="https://en.wikipedia.org/wiki/High_Efficiency_Image_File_Format" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on HEIF/HEIC</a>.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/what-is-heif-format.jpg"
                    alt="Professional Analysis of HEIC Format"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* PNG Details Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://image.heic-tojpg.com/the-wikipedia-page-on-png.webp"
                    alt="Detailed Explanation of PNG Files"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is a PNG File?</h3>
                    <p className="text-gray-600">
                      PNG (Portable Network Graphics) is a raster graphics file format that supports lossless data compression and transparency. It uses DEFLATE compression algorithm and supports up to 16 million colors with alpha channel transparency.
                      PNG files are widely supported across all platforms and applications, making them ideal when you need to convert HEIC to PNG for maximum compatibility. You can visit: <a href="https://en.wikipedia.org/wiki/PNG" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on PNG</a>.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Metadata in Image Files?</h3>
                    <p className="text-gray-600">
                      Image metadata includes information about the file such as creation date, device information, and sometimes location data. When using our HEIC to PNG converter,
                      you can choose to preserve or remove this metadata during the conversion process. Removing metadata can be crucial for privacy protection and reducing file size when you convert .HEIC to .PNG format.
                    </p>
                  </div>
                </div>
              </div>

              {/* Format Comparison Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">HEIC vs. PNG: Understanding the Differences</h3>
                    <p className="text-gray-600">
                      While HEIC offers superior compression using the HEVC codec and temporal encoding techniques, PNG provides universal compatibility with its lossless DEFLATE compression algorithm.
                      HEIC can be up to 50% smaller than PNGs while maintaining comparable visual quality, but PNG's widespread support makes it essential to convert HEIC to PNG for certain applications and platforms.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Which Image Formats Can I Upload?</h3>
                    <p className="text-gray-600">
                      Our HEIC to PNG converter primarily supports HEIC files (.heic extension) and HEIF files (.heif extension). This specialized tool is designed to efficiently convert .HEIC to PNG format while preserving all image attributes including transparency, color profiles, and image quality.
                      The conversion process utilizes advanced palette optimization and intelligent color mapping to ensure optimal results.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/metadata-in-image.webp"
                    alt="HEIC vs PNG Format Comparison"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Conversion Benefits Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg"
                    alt="Benefits of HEIC to PNG Conversion"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Why Convert HEIC to PNG?</h3>
                    <p className="text-gray-600">
                      Converting HEIC to PNG ensures maximum compatibility across all applications, platforms, and devices. While HEIC offers excellent compression on Apple devices, many graphic design applications, content management systems, and Windows-based software don't fully support HEIC.
                      Using our HEIC to PNG converter provides universal compatibility, eliminating potential issues when editing or sharing your iPhone and iPad photos.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Benefits of PNG Format</h3>
                    <p className="text-gray-600">
                      PNG files offer several technical advantages including lossless compression, full alpha channel transparency support, and gamma correction capabilities. When you convert HEIC to PNG, you gain access to these benefits
                      plus widespread compatibility with virtually all image editing software, web platforms, and operating systems, making PNG an excellent universal format for both web and print applications.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="how-to">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use the HEIC to PNG Converter</h2>

            <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
              <div>
                <ol className="space-y-6 relative">
                  <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload HEIC Files</h3>
                    <p className="text-gray-600">
                      Drag and drop your HEIC files into the conversion area, or click to select files from your device. Our HEIC to PNG converter supports batch uploading of multiple files for simultaneous processing, increasing efficiency for iPhone and iPad users.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Conversion Settings</h3>
                    <p className="text-gray-600">
                      Adjust HEIC to PNG converter settings to optimize your output. You can choose quality levels and decide whether to preserve or remove metadata from your images for enhanced privacy protection when converting from .HEIC to PNG format.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert and Download</h3>
                    <p className="text-gray-600">
                      Click the "Convert" button to start the HEIC to PNG conversion process. Our advanced HEVC decoder transforms your Apple HEIC files to universally compatible PNG format. Once completed, you can download individual PNG files or use our batch download option.
                    </p>
                  </li>
                </ol>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg"
                  alt="HEIC to PNG Conversion Process"
                  className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                />
              </div>
            </div>
          </section>
        </div>


        <section className="why-use mb-8 mt-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our HEIC to PNG Converter</h2>

          <div className="space-y-16">
            {/* Reason 1 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Universal Compatibility</h3>
                <p className="text-gray-600">
                  While HEIC offers excellent compression using the HEVC codec, many applications and devices outside the Apple ecosystem don't support this format. Our HEIC to PNG converter ensures your photos can be viewed, edited, and shared across all platforms without compatibility issues.
                  The conversion process preserves all image attributes including color depth and profiles.
                </p>
                <p className="text-gray-600">
                  Our HEIC to PNG converter maintains optimal image quality while changing the file format, using advanced proprietary algorithms to decode the HEIF container structure and accurately translate the HEVC-encoded image data to PNG format.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg"
                  alt="HEIC to PNG Universal Compatibility"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 2 - Brand DNA Analysis */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg"
                  alt="Simplified HEIC to PNG Workflow"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Simplified Design Workflow - Our Brand DNA</h3>
                <p className="text-gray-600">
                  Our brand is built around simplifying complex technical processes. Converting HEIC to PNG exemplifies this core value by eliminating compatibility barriers when working with iPhone photos across multiple applications. Our HEIC to PNG converter 
                  transforms Apple's proprietary format into the universally recognized PNG standard with just a few clicks.
                </p>
                <p className="text-gray-600">
                  The HEIC to PNG converter's batch processing feature allows you to convert multiple .HEIC to .PNG files simultaneously, supporting parallel multi-task processing that saves valuable time and effort in your design or development workflow.
                </p>
              </div>
            </div>

            {/* Reason 3 - User Pain Point Mapping */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Privacy and Security - Addressing User Pain Points</h3>
                <p className="text-gray-600">
                  We've identified key user challenges: privacy concerns when sharing iPhone photos and frustration with incompatible HEIC files. Using our HEIC to PNG converter tool, you can choose to remove metadata from your images during conversion,
                  allowing you to share photos without revealing sensitive information such as GPS coordinates or device details when you convert HEIC to PNG.
                </p>
                <p className="text-gray-600">
                  Our secure conversion infrastructure employs TLS encryption and secure file handling protocols, ensuring your images remain private throughout the HEIC to PNG conversion process. All uploaded files are automatically deleted after processing,
                  providing peace of mind for security-conscious users switching from HEIC to PNG format.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg"
                  alt="HEIC to PNG Privacy Protection"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 4 - Value Proposition & Differentiation */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp"
                  alt="HEIC to PNG Quality Preservation"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Professional Image Quality - Our Unique Value</h3>
                <p className="text-gray-600">
                  What sets us apart is our specialized focus on HEIC file handling. Our HEIC to PNG converter uses proprietary HEVC decoder technology that ensures the highest quality output compared to generic converters. The conversion process preserves full color depth and accuracy,
                  making it ideal for professional photographers, graphic designers, and Apple device users who need to convert HEIC to PNG without quality loss.
                </p>
                <p className="text-gray-600">
                  Unlike competitors who use generic conversion methods, our HEIC to PNG converter is specifically optimized for Apple's HEIF implementation, ensuring perfect rendering of every pixel detail when you convert .HEIC to .PNG, making it perfect for 
                  professional photography, product images, and creative projects that demand absolute fidelity.
                </p>
              </div>
            </div>
          </div>
        </section>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About HEIC to PNG Conversion</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900">What's the difference between HEIC and PNG?</h3>
              <p className="mt-1 text-gray-700">
                HEIC is Apple's implementation of the HEIF format that uses HEVC (H.265) encoding for superior compression. PNG is a universal format that uses lossless compression exclusively.
                While HEIC files are typically 50% smaller than equivalent quality image files, PNG offers better compatibility across all platforms and applications, which is why many users need to convert HEIC to PNG.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Will I lose quality when converting HEIC to PNG?</h3>
              <p className="mt-1 text-gray-700">
                When converting from HEIC to PNG using our converter, there is virtually no quality loss as PNG uses lossless compression. Our specialized HEIC to PNG converter ensures optimal image fidelity through advanced HEVC decoding algorithms
                and intelligent color profile mapping. The resulting PNG files maintain all the visual quality of your original iPhone or iPad photos.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Is it safe to convert HEIC to PNG online?</h3>
              <p className="mt-1 text-gray-700">
                Yes, our online HEIC to PNG converter follows strict security protocols when handling all files. Your images are briefly processed on our secure servers and then automatically deleted.
                We employ military-grade encryption for the conversion process, ensuring your personal photos remain completely private. All conversion processes take place in a TLS-encrypted secure environment, ensuring your HEIC to PNG conversion
                is completely safe and reliable.
              </p>
            </div>
          </div>
        </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 