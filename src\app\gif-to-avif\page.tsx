'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function GifToAvif() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');
    
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/gif-to-avif');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/gif-to-avif');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    
    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.gif');
    });
    
    if (invalidFiles.length > 0) {
      setError('Please only upload GIF files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/gif': ['.gif', '.GIF']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload GIF files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });
    
    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev => 
          prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'converting' } 
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/gif-to-avif', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'done', downloadUrl: result.url } 
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'error', errorMessage: errorMessage } 
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.gif', '').replace('.GIF', '') + '.avif';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free GIF to AVIF Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert GIF images to modern AVIF format online
        </p>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          <div className="mb-6 md:mb-8 relative">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive 
                  ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80' 
                  : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
                } shadow-sm hover:shadow-md`}
            >
              <input {...getInputProps()} accept=".gif,.GIF" />
              <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
              <p className="text-lg md:text-xl mb-2 text-gray-800">Select GIF Images</p>
              <p className="text-sm text-gray-600 mb-2">from your device</p>
              <p className="text-xs text-gray-500">Supports up to 100 files</p>
              
              {isMobile && (
                <div className="mt-4 text-sm text-indigo-600 font-medium">
                  Tap here to select photos from your device
                </div>
              )}
            </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
          </div>
          
          {files.length > 0 && (
            <div className="space-y-4 md:space-y-6">
              <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
                <table className="min-w-full table-fixed">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                      <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {files.map((file, index) => (
                      <tr key={file.id}>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                        <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                          <div className="truncate" title={file.name}>
                            {isMobile ? 
                              file.name.length > 10 ? 
                                file.name.slice(0, 7) + '...' + file.name.slice(-3) 
                                : file.name
                              : file.name
                            }
                          </div>
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                          {file.status === 'done' && file.downloadUrl ? (
                            <a 
                              href={file.downloadUrl} 
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              download
                            >
                              Download
                            </a>
                          ) : file.status === 'error' ? (
                            <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                          ) : file.status === 'converting' ? (
                            <span className="text-yellow-500 text-sm">Converting...</span>
                          ) : (
                            <span className="text-gray-500 text-sm">Waiting</span>
                          )}
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                          {file.status === 'done' ? (
                            <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                          ) : (
                            <button 
                              onClick={() => handleDeleteFile(file.id)}
                              className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                              disabled={file.status === 'converting'}
                              title="Delete File"
                            >
                              <FiX className="w-5 h-5" />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
                <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                    <select
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value="AVIF"
                      disabled
                    >
                      <option>AVIF</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="range"
                        min="1"
                        max="100"
                        value={quality}
                        onChange={(e) => setQuality(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-12">{quality}%</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={removeExif}
                      onChange={(e) => setRemoveExif(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Remove all EXIF information</span>
                  </label>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={consentPrivacy}
                    onChange={(e) => setConsentPrivacy(e.target.checked)}
                    className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    required
                  />
                  <label className="text-sm text-gray-600">
                    I consent to heic-tojpg.com collecting and processing my data according to{' '}
                    <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                      Privacy Policy
                    </Link>
                    .
                  </label>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                  <button
                    onClick={handleClearAll}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    disabled={isConverting}
                  >
                    Clear all
                  </button>
                  <button
                    onClick={handleConvert}
                    disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {isConverting ? 'Converting...' : 'Convert'}
                  </button>

                  <button
                    onClick={handleDownloadAll}
                    disabled={!files.some(f => f.status === 'done')}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                  >
                    <FiDownload className="w-4 h-4" />
                    <span>Download all</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Share buttons */}
          <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
            <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
            <div className="sharethis-inline-share-buttons"></div>
          </div>
          {/* Related tools */}
          <RelatedTools currentTool="GIF to AVIF" />

          {/* Conversion progress indicator */}
          {isConverting && isMobile && (
            <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
              <div className="text-center text-sm text-indigo-800 font-medium">
                Converting: {convertProgress.current} of {convertProgress.total} files
              </div>
              <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300" 
                  style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          <div className="mt-12 space-y-16">
            <section className="introduction">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">
                <a href="/gif-to-avif" className="hover:text-indigo-600 transition-colors">GIF to AVIF</a> Converter Features
              </h2>
              <div className="space-y-16">
                {/* Feature Group 1: Free & Easy + Fast Conversion */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                        Professional GIF to AVIF Conversion - 100% Free
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>No registration or payment required - completely free tool to convert GIF to AVIF with enterprise-grade quality</li>
                        <li>Intuitive drag-and-drop interface with one-click upload functionality for efficient batch processing</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                        High-Speed GIF to AVIF Conversion with Animation Preservation
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Using advanced AVIF encoding algorithms for frame-perfect GIF file conversion</li>
                        <li>Maintains animation timing and sequencing while reducing file size by up to 90% with AV1 codec technology</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/gif-to-avif-converter-tool.jpg" 
                      alt="Professional GIF to AVIF Converter Tool" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 2: Security & Privacy */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg" 
                      alt="Online Convert GIF to AVIF Format Converter" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                        Watermark-Free & Unlimited GIF to AVIF Converter
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Converted AVIF files are completely watermark-free, ready for professional web projects, applications, or social media</li>
                        <li>No file size limits, no quantity restrictions - convert GIF to AVIF anytime, anywhere</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                        GIF to AVIF – End-to-End Encryption & Privacy Protection
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Employing AES-256 bit encryption standards to ensure GIF file security during transmission and processing</li>
                        <li>Using convert-and-delete technology - files are immediately removed from servers after .gif to .avif conversion</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Feature Group 3: Batch Processing & Compatibility */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                        Efficient Batch GIF to AVIF Conversion Technology
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Multi-threaded processing technology to simultaneously convert multiple GIF files to AVIF format</li>
                        <li>Perfect for web developers and content creators who need to convert GIF to AVIF for modern web optimization</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                        Cross-Platform GIF to AVIF Converter Compatibility
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Our GIF to AVIF converter supports all major browsers including Chrome, Firefox, Edge, Safari and more</li>
                        <li>Compatible with Windows, macOS, Linux, Android, and iOS devices - a truly universal GIF to AVIF converter free for all users</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg" 
                      alt="Batch GIF to AVIF Conversion Compatibility" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 4: Quality Control & Online Access */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg" 
                      alt="Online GIF to AVIF Quality Control" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                        Professional GIF to AVIF Converter with Advanced Image Processing
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Optimized AVIF compression parameters, balancing animation quality and file size for professional results</li>
                        <li>Supports 10-bit color depth and HDR conversion when you convert GIF to AVIF for next-generation visual experiences</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                        Cloud-Based GIF to AVIF Conversion - No Software Installation Required
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Pure cloud processing - no software or plugins needed to convert GIF to AVIF files</li>
                        <li>WebAssembly optimization technology for efficient browser-based processing on any device</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="what-is">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the GIF to AVIF Converter</h2>
              
              <div className="space-y-16">
                {/* GIF Introduction Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is GIF to AVIF Conversion?</h3>
                      <p className="text-gray-600">
                        GIF to AVIF conversion is the process of transforming the classic Graphics Interchange Format (GIF) animations into the modern AV1 Image File Format (AVIF). While GIF has been the standard for simple animations for decades, 
                        AVIF offers significantly better compression using the AV1 video codec technology. Our GIF to AVIF converter ensures full preservation of animation frames and timing while dramatically reducing file sizes.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is the GIF Format?</h3>
                      <p className="text-gray-600">
                        GIF (Graphics Interchange Format) is a legacy image format developed in 1987 that supports animations through frame sequencing. It uses lossless LZW compression but is limited to a 256-color palette per frame,
                        resulting in larger file sizes for complex animations. Despite these limitations, GIF remains popular for simple animations and reactions, though modern use cases increasingly demand a more efficient format like AVIF, making 
                        a reliable GIF to AVIF converter essential. For more information, visit: <a href="https://en.wikipedia.org/wiki/GIF" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on GIF</a>.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/the-wikipedia-page-on-gif.webp" 
                      alt="Professional Analysis of GIF Format" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* AVIF Details Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/the-wikipedia-page-on-avif.webp" 
                      alt="Detailed Explanation of AVIF Files" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is an AVIF File?</h3>
                      <p className="text-gray-600">
                        AVIF (AV1 Image File Format) is a cutting-edge image format based on the AV1 video codec. It offers superior compression efficiency, supporting both lossy and lossless compression with 10-bit color depth, HDR, and wide color gamut capabilities.
                        AVIF provides dramatically smaller file sizes compared to GIF while maintaining higher image quality. Using a GIF to AVIF converter free tool like ours helps content creators embrace this next-generation format. Learn more at: <a href="https://en.wikipedia.org/wiki/AVIF" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on AVIF</a>.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Animation Frame Rate in GIF to AVIF Conversion?</h3>
                      <p className="text-gray-600">
                        Animation frame rate determines how smoothly animations play and is measured in frames per second (FPS). When using our .gif to .avif converter, 
                        all timing information from the original GIF is preserved in the AVIF output. AVIF's implementation of AV1 intra-frame encoding maintains precise timing while offering much better compression
                        compared to GIF's simpler frame sequencing approach.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Format Comparison Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">GIF vs. AVIF: Understanding the Differences</h3>
                      <p className="text-gray-600">
                        While GIF offers universal compatibility with its established 8-bit indexed color format and LZW compression, AVIF provides vastly superior compression using the AV1 codec's intra-frame encoding techniques. 
                        AVIF files can be up to 90% smaller than equivalent GIFs while maintaining better color reproduction and detail. For modern web applications and bandwidth-conscious scenarios, using a GIF to AVIF converter is essential for optimal user experience.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Which Image Formats Can I Upload?</h3>
                      <p className="text-gray-600">
                        Our GIF to AVIF converter primarily supports GIF files (.gif extension). This specialized tool is designed to efficiently convert GIF to AVIF format while preserving animation sequences, timing information, and optimizing for web delivery.
                        The conversion process utilizes advanced intra-frame encoding and chroma subsampling techniques to ensure optimal results when transforming legacy animations to next-generation formats.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/metadata-in-image.webp" 
                      alt="GIF vs AVIF Format Comparison" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Conversion Benefits Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg" 
                      alt="Benefits of GIF to AVIF Conversion" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Why Convert GIF to AVIF?</h3>
                      <p className="text-gray-600">
                        Converting GIF to AVIF dramatically reduces file sizes while preserving or even enhancing visual quality. This is particularly important for modern web applications where bandwidth and loading speed are critical factors.
                        Using our .gif to avif converter helps content creators and web developers deliver faster-loading, more engaging animated content while reducing hosting costs and improving SEO rankings through better page performance metrics.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Benefits of AVIF Format</h3>
                      <p className="text-gray-600">
                        AVIF files offer several technical advantages including superior compression efficiency, 10-bit color depth support, HDR capability, and both lossy and lossless encoding options. When you convert GIF to AVIF, you gain access to these benefits
                        plus improved animation rendering through advanced intra-frame encoding. As browser support continues to grow, AVIF is becoming the preferred format for high-quality, bandwidth-efficient animated content delivery.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="how-to">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use the GIF to AVIF Converter</h2>
              
              <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
                <div>
                  <ol className="space-y-6 relative">
                    <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload GIF Files</h3>
                      <p className="text-gray-600">
                        Drag and drop your GIF files into the conversion area, or click to select files from your device. Our GIF to AVIF converter supports batch uploading of multiple files for simultaneous processing, increasing efficiency.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Conversion Settings</h3>
                      <p className="text-gray-600">
                        Adjust GIF to AVIF converter settings to optimize your output. You can select quality levels and choose to preserve or remove metadata from your images when converting from GIF to AVIF format.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert and Download</h3>
                      <p className="text-gray-600">
                        Click the "Convert" button to start the GIF to AVIF conversion process. Once completed, you can download AVIF files individually or use our batch download option to download all converted files at once.
                      </p>
                    </li>
                  </ol>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg" 
                    alt="GIF to AVIF Conversion Process" 
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>
            </section>
          </div>

          
          <section className="why-use mb-8 mt-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our GIF to AVIF Converter</h2>
            
            <div className="space-y-16">
              {/* Reason 1 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800">Next-Generation Animation Technology</h3>
                  <p className="text-gray-600">
                    While GIF has been the standard for simple web animations for decades, its outdated compression technology results in large file sizes. Our GIF to AVIF converter leverages the cutting-edge AV1 video codec to reduce animation sizes by up to 90% while maintaining visual fidelity.
                    The conversion process preserves frame timing, transparency, and animation sequences for a perfect reproduction.
                  </p>
                  <p className="text-gray-600">
                    Our GIF to AVIF converter maintains optimal image quality while transforming animations, using advanced techniques like frame analysis, motion compensation, and perceptual optimization to provide the highest quality conversion possible.
                  </p>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg" 
                    alt="GIF to AVIF Advanced Animation Technology" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
              
              {/* Reason 2 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg" 
                    alt="Optimized GIF to AVIF Workflow" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
                <div className="space-y-4 order-1 md:order-2">
                  <h3 className="text-lg font-semibold text-gray-800">Web Performance Optimization</h3>
                  <p className="text-gray-600">
                    Converting GIF to AVIF significantly improves web page performance by reducing animation file sizes without compromising quality. This results in faster loading times, lower bandwidth costs, and improved core web vital metrics,
                    particularly for pages with multiple animated elements that would otherwise be heavy GIF files.
                  </p>
                  <p className="text-gray-600">
                    Our GIF to AVIF converter free tool enables developers and content creators to implement cutting-edge image optimization strategies without requiring specialized knowledge or software. The batch processing feature supports converting multiple .gif to .avif files simultaneously,
                    saving valuable time in your development or content creation workflow.
                  </p>
                </div>
              </div>
              
              {/* Reason 3 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800">Brand DNA Analysis & Future-Proof Technology</h3>
                  <p className="text-gray-600">
                    Our GIF to AVIF converter is built with careful attention to brand identity preservation. When converting animations that represent your brand, maintaining color accuracy and motion fidelity is crucial.
                    Our conversion engine implements advanced color management systems that ensure your brand colors remain consistent when you convert GIF to AVIF.
                  </p>
                  <p className="text-gray-600">
                    By adopting AVIF through our GIF to AVIF converter, you're future-proofing your content for the next generation of web standards. As browser support for AVIF continues to expand,
                    your animations will be ready to deliver superior experiences while remaining backward compatible through our intelligent fallback systems.
                  </p>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg" 
                    alt="GIF to AVIF Brand Identity Preservation" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
              
              {/* Reason 4 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img 
                    src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp" 
                    alt="GIF to AVIF User Pain Point Solutions" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
                <div className="space-y-4 order-1 md:order-2">
                  <h3 className="text-lg font-semibold text-gray-800">User Pain Point Solutions</h3>
                  <p className="text-gray-600">
                    Our GIF to AVIF converter directly addresses key pain points for web developers and content creators: excessive bandwidth usage, slow-loading animations, and poor mobile experience. By providing an efficient and free GIF to AVIF converter,
                    we enable you to deliver smooth, responsive animated content even on slower connections and mobile devices.
                  </p>
                  <p className="text-gray-600">
                    The differentiating value proposition of our GIF to AVIF converter is the perfect balance of quality preservation and size reduction. Through sophisticated encoding parameters optimization,
                    we ensure that your converted animations maintain the visual impact of the original GIFs while delivering the performance benefits of the AVIF format.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About GIF to AVIF Conversion</h2>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-900">What's the difference between GIF and AVIF?</h3>
                <p className="mt-1 text-gray-700">
                  GIF is a legacy format from 1987 that uses LZW compression and is limited to 256 colors per frame, resulting in large file sizes for animations. AVIF is a modern format based on the AV1 video codec that offers superior compression,
                  10-bit color depth, HDR support, and both lossy and lossless options. When you convert GIF to AVIF, file sizes can be reduced by up to 90% while maintaining or improving visual quality.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Will I lose animation quality when converting GIF to AVIF?</h3>
                <p className="mt-1 text-gray-700">
                  When converting from GIF to AVIF using our converter, you'll typically see improved quality as AVIF supports better color reproduction and more efficient encoding of animation frames. Our GIF to AVIF converter uses sophisticated algorithms to preserve timing information
                  and animation sequencing while applying optimal compression parameters to ensure animations look even better in AVIF format than in the original GIF.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Is it safe to use a GIF to AVIF converter online?</h3>
                <p className="mt-1 text-gray-700">
                  Yes, our online GIF to AVIF converter follows strict security protocols when handling all files. Your images are briefly processed on our secure servers and then automatically deleted.
                  We don't permanently store your uploaded files or use them for any other purpose. All conversion processes take place in a TLS-encrypted secure environment, ensuring your GIF to AVIF conversion
                  is completely safe and reliable.
                </p>
              </div>
            </div>
          </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 