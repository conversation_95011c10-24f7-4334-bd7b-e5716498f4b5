'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function HeicToAvif() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');

    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/heic-to-avif');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/heic-to-avif');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);

    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.heic') && !lowerName.endsWith('.heif');
    });

    if (invalidFiles.length > 0) {
      setError('Please only upload HEIC/HEIF files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/heic': ['.heic', '.HEIC'],
      'image/heif': ['.heif', '.HEIF']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload HEIC/HEIF files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });

    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev =>
          prev.map(f =>
            f.id === fileItem.id
              ? { ...f, status: 'converting' }
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/heic-to-avif', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'done', downloadUrl: result.url }
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'error', errorMessage: errorMessage }
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.heic', '').replace('.HEIC', '').replace('.heif', '').replace('.HEIF', '') + '.avif';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free HEIC to AVIF Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert HEIC photos to modern AVIF format online
        </p>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="mb-6 md:mb-8 relative">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive
                ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80'
                : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
              } shadow-sm hover:shadow-md`}
          >
            <input {...getInputProps()} accept=".heic,.HEIC,.heif,.HEIF" />
            <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
            <p className="text-lg md:text-xl mb-2 text-gray-800">Select HEIC Photos</p>
            <p className="text-sm text-gray-600 mb-2">from your device</p>
            <p className="text-xs text-gray-500">Supports up to 100 files</p>

            {isMobile && (
              <div className="mt-4 text-sm text-indigo-600 font-medium">
                Tap here to select photos from your device
              </div>
            )}
          </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
        </div>

        {files.length > 0 && (
          <div className="space-y-4 md:space-y-6">
            <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
              <table className="min-w-full table-fixed">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {files.map((file, index) => (
                    <tr key={file.id}>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                      <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                        <div className="truncate" title={file.name}>
                          {isMobile ?
                            file.name.length > 10 ?
                              file.name.slice(0, 7) + '...' + file.name.slice(-3)
                              : file.name
                            : file.name
                          }
                        </div>
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                        {file.status === 'done' && file.downloadUrl ? (
                          <a
                            href={file.downloadUrl}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            download
                          >
                            Download
                          </a>
                        ) : file.status === 'error' ? (
                          <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                        ) : file.status === 'converting' ? (
                          <span className="text-yellow-500 text-sm">Converting...</span>
                        ) : (
                          <span className="text-gray-500 text-sm">Waiting</span>
                        )}
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                        {file.status === 'done' ? (
                          <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                        ) : (
                          <button
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                            disabled={file.status === 'converting'}
                            title="Delete File"
                          >
                            <FiX className="w-5 h-5" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
              <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                  <select
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value="AVIF"
                    disabled
                  >
                    <option>AVIF</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={quality}
                      onChange={(e) => setQuality(parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-600 w-12">{quality}%</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={removeExif}
                    onChange={(e) => setRemoveExif(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Remove all EXIF information</span>
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={consentPrivacy}
                  onChange={(e) => setConsentPrivacy(e.target.checked)}
                  className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  required
                />
                <label className="text-sm text-gray-600">
                  I consent to heic-tojpg.com collecting and processing my data according to{' '}
                  <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                    Privacy Policy
                  </Link>
                  .
                </label>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                <button
                  onClick={handleClearAll}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                  disabled={isConverting}
                >
                  Clear all
                </button>
                <button
                  onClick={handleConvert}
                  disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isConverting ? 'Converting...' : 'Convert'}
                </button>

                <button
                  onClick={handleDownloadAll}
                  disabled={!files.some(f => f.status === 'done')}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                >
                  <FiDownload className="w-4 h-4" />
                  <span>Download all</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Share buttons */}
        <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
          <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
          <div className="sharethis-inline-share-buttons"></div>
        </div>
        {/* Related tools */}
        <RelatedTools currentTool="HEIC to AVIF" />

        {/* Conversion progress indicator */}
        {isConverting && isMobile && (
          <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
            <div className="text-center text-sm text-indigo-800 font-medium">
              Converting: {convertProgress.current} of {convertProgress.total} files
            </div>
            <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300"
                style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
              />
            </div>
          </div>
        )}

        <div className="mt-12 space-y-16">
          <section className="introduction">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              <a href="/heic-to-avif" className="hover:text-indigo-600 transition-colors">HEIC to AVIF</a> Converter Features
            </h2>
            <div className="space-y-16">
              {/* Feature Group 1: Free & Easy + Fast Conversion */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                      Professional HEIC to AVIF Conversion - 100% Free
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>No registration or payment required - completely free tool to convert HEIC to AVIF with enterprise-grade quality</li>
                      <li>Intuitive drag-and-drop interface with one-click upload functionality for efficient batch processing</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                      High-Speed HEIC to AVIF Conversion with Advanced Compression
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Using AV1 Image File Format technology for optimized AVIF encoding with superior compression ratios</li>
                      <li>Maintains full color depth and HDR capabilities with intelligent chroma subsampling for optimal quality-to-size ratio</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/heic-to-avif-converter-tool.webp"
                    alt="Professional HEIC to AVIF Converter Tool"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 2: Security & Privacy */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg"
                    alt="Online Convert HEIC to AVIF Format Converter"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                      Watermark-Free & Unlimited HEIC to AVIF Converter
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Converted AVIF files are completely watermark-free, ready for professional media workflows, websites, or mobile applications</li>
                      <li>No file size limits, no quantity restrictions - convert HEIC to AVIF anytime, anywhere</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                      HEIC to AVIF – End-to-End Encryption & Privacy Protection
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Employing AES-256 bit encryption standards to ensure HEIC file security during transmission and processing</li>
                      <li>Using convert-and-delete technology - files are immediately removed from servers after .heic to .avif conversion</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Feature Group 3: Batch Processing & Compatibility */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                      Efficient Batch HEIC to AVIF Conversion Technology
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Multi-threaded processing technology to simultaneously convert multiple HEIC files to AVIF format</li>
                      <li>Perfect for photographers and developers who need a free HEIC to AVIF converter for next-generation image optimization</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                      Cross-Platform HEIC to AVIF Conversion Compatibility
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>HEIC to AVIF converter supports all major browsers including Chrome, Firefox, Edge, Safari and more</li>
                      <li>Compatible with Windows, macOS, Linux, Android, and iOS devices - a truly universal .heic to avif converter</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg"
                    alt="Batch HEIC to AVIF Conversion Compatibility"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 4: Quality Control & Online Access */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg"
                    alt="Online HEIC to AVIF Quality Control"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                      Professional HEIC to AVIF Converter with Advanced Image Processing
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Customizable AVIF compression parameters, balancing image quality and file size for professional results</li>
                      <li>Supports HDR to SDR tone-mapping and ICC color profile management when you convert HEIC to AVIF</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                      Cloud-Based HEIC to AVIF Conversion - No Software Installation Required
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Pure cloud processing - no software or plugins needed to convert HEIC to AVIF files</li>
                      <li>WebAssembly optimization technology for efficient browser-based processing on any device</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="what-is">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the HEIC to AVIF Converter</h2>

            <div className="space-y-16">
              {/* HEIC Introduction Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is HEIC to AVIF Conversion?</h3>
                    <p className="text-gray-600">
                      HEIC to AVIF conversion is the process of transforming Apple's HEIC format images into the next-generation AVIF format. While HEIC offers excellent compression using HEVC codec technology,
                      AVIF uses the newer AV1 video codec for even better compression ratios with similar or better quality. Our HEIC to AVIF converter ensures full preservation of image fidelity during conversion.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is the HEIC Format?</h3>
                    <p className="text-gray-600">
                      HEIC (High Efficiency Image Format) is Apple's implementation of the HEIF (High Efficiency Image File Format) standard that uses HEVC (H.265) compression. It provides superior compression compared to JPEG while maintaining higher quality.
                      HEIC supports 16-bit color, transparency, and multiple images in a single file. Despite these advantages, compatibility issues make a reliable HEIC to AVIF converter essential for cross-platform usage and future-proofing image assets.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/what-is-heif-format.jpg"
                    alt="Professional Analysis of HEIC Format"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* AVIF Details Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://image.heic-tojpg.com/the-wikipedia-page-on-avif.webp"
                    alt="Detailed Explanation of AVIF Files"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is an AVIF File?</h3>
                    <p className="text-gray-600">
                      AVIF (AV1 Image File Format) is a modern image format based on the AV1 video codec developed by the Alliance for Open Media. It offers superior compression efficiency with excellent perceptual quality, HDR support, wide color gamut, and transparency capabilities.
                      AVIF can achieve file sizes 30-50% smaller than JPEG or HEIC at equivalent visual quality, making our HEIC to AVIF converter an essential tool for optimizing visual content.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Metadata in Image Files?</h3>
                    <p className="text-gray-600">
                      Image metadata includes information about the file such as camera settings, creation date, GPS coordinates, and device information. When using our HEIC to AVIF converter,
                      you can choose to preserve or remove this metadata during the conversion process. Removing metadata can be crucial for privacy protection and reducing file size when you convert .heic to .avif format.
                    </p>
                  </div>
                </div>
              </div>

              {/* Format Comparison Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">HEIC vs. AVIF: Understanding the Differences</h3>
                    <p className="text-gray-600">
                      While HEIC uses HEVC (H.265) compression technology, AVIF employs the newer AV1 video codec for enhanced compression efficiency. AVIF can be up to 50% smaller than HEIC files while maintaining similar visual quality.
                      Both formats support HDR, wide color gamut, and alpha transparency, but AVIF offers better compression ratios, making our HEIC to AVIF converter valuable for optimizing storage and bandwidth.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Which Image Formats Can I Upload?</h3>
                    <p className="text-gray-600">
                      Our HEIC to AVIF converter primarily supports HEIC and HEIF files (.heic and .heif extensions). This specialized tool is designed to efficiently convert HEIC to AVIF format while preserving all image attributes including bit depth, color profiles, and HDR characteristics.
                      The conversion process utilizes advanced encoding algorithms to ensure optimal quality-to-compression ratio.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/metadata-in-image.webp"
                    alt="HEIC vs AVIF Format Comparison"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Conversion Benefits Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg"
                    alt="Benefits of HEIC to AVIF Conversion"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Why Convert HEIC to AVIF?</h3>
                    <p className="text-gray-600">
                      Converting HEIC to AVIF offers several advantages for web developers, content creators, and digital asset managers. AVIF provides better compression efficiency than HEIC, resulting in smaller file sizes without sacrificing quality.
                      Using our free HEIC to AVIF converter enables you to leverage the latest image compression technology for faster loading websites and applications while preserving visual fidelity.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Benefits of AVIF Format</h3>
                    <p className="text-gray-600">
                      AVIF files offer numerous technical advantages including superior compression efficiency, 10/12-bit color depth support, full alpha channel transparency, and HDR capabilities. When you convert .heic to avif,
                      you gain all these benefits plus growing browser support. AVIF's advanced perceptual quantization algorithms deliver better quality-to-size ratio than any other modern image format.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="how-to">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use the HEIC to AVIF Converter</h2>

            <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
              <div>
                <ol className="space-y-6 relative">
                  <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload HEIC Files</h3>
                    <p className="text-gray-600">
                      Drag and drop your HEIC files into the conversion area, or click to select files from your device. Our HEIC to AVIF converter supports batch uploading of multiple files for simultaneous processing, increasing efficiency.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Conversion Settings</h3>
                    <p className="text-gray-600">
                      Adjust HEIC to AVIF converter settings to optimize your output. Select quality level and choose to preserve or remove metadata from your images for enhanced privacy protection when converting from HEIC to AVIF format.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert and Download</h3>
                    <p className="text-gray-600">
                      Click the "Convert" button to start the HEIC to AVIF conversion process. Once completed, you can download AVIF files individually or use our batch download option to download all converted files at once.
                    </p>
                  </li>
                </ol>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg"
                  alt="HEIC to AVIF Conversion Process"
                  className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                />
              </div>
            </div>
          </section>
        </div>


        <section className="why-use mb-8 mt-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our HEIC to AVIF Converter</h2>

          <div className="space-y-16">
            {/* Reason 1 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Future-Proof Image Format</h3>
                <p className="text-gray-600">
                  AVIF represents the cutting edge in image compression technology. By using our HEIC to AVIF converter, you're adopting the most advanced image format available today. AVIF offers superior compression efficiency, better visual quality at low bitrates, and support for high bit-depth content.
                  The conversion process preserves all important image characteristics including HDR metadata and color profiles.
                </p>
                <p className="text-gray-600">
                  Our HEIC to AVIF converter maintains optimal image quality while delivering significantly smaller file sizes, using perceptual quantization and advanced entropy coding techniques to provide the highest fidelity conversion possible.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg"
                  alt="HEIC to AVIF Future-Proof Format"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 2 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg"
                  alt="Simplified HEIC to AVIF Workflow"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Optimized Web Performance</h3>
                <p className="text-gray-600">
                  When implementing responsive images and optimizing web performance, file size matters significantly. Converting HEIC to AVIF with our free HEIC to AVIF converter tool helps reduce page load times and bandwidth consumption,
                  particularly important for mobile users and markets with limited connectivity.
                </p>
                <p className="text-gray-600">
                  Our HEIC to AVIF converter's batch processing feature allows you to convert multiple .heic to .avif files simultaneously, supporting parallel multi-task processing that saves valuable time and effort in your content optimization workflow.
                </p>
              </div>
            </div>

            {/* Reason 3 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Privacy and Security</h3>
                <p className="text-gray-600">
                  Using our HEIC to AVIF converter tool, you can choose to remove metadata from your images during the conversion process. This feature is particularly important for privacy-conscious users,
                  allowing you to share images without revealing sensitive information such as GPS coordinates or device details when you convert HEIC to AVIF.
                </p>
                <p className="text-gray-600">
                  Our secure conversion infrastructure employs TLS encryption and secure file handling protocols, ensuring your images remain private throughout the HEIC to AVIF conversion process. All uploaded files are automatically deleted after processing,
                  providing peace of mind for security-conscious users.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg"
                  alt="HEIC to AVIF Privacy Protection"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 4 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp"
                  alt="HEIC to AVIF Quality Preservation"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Professional Image Quality</h3>
                <p className="text-gray-600">
                  Our HEIC to AVIF converter uses advanced image processing algorithms to ensure the highest quality output. The conversion process preserves HDR characteristics, color depth, and alpha channel transparency,
                  making it ideal for professional photographers, web developers, and digital content creators who need to convert HEIC to AVIF without quality loss.
                </p>
                <p className="text-gray-600">
                  The AVIF format's intelligent chroma subsampling and advanced quantization ensures that every important visual detail is preserved when you convert HEIC to AVIF, making it perfect for both photographic content and computer-generated imagery.
                </p>
              </div>
            </div>
          </div>
        </section>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About HEIC to AVIF Conversion</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900">What's the difference between HEIC and AVIF?</h3>
              <p className="mt-1 text-gray-700">
                HEIC is Apple's implementation of the HEIF standard using HEVC (H.265) compression, while AVIF is based on the AV1 video codec developed by the Alliance for Open Media.
                AVIF typically achieves 30-50% better compression than HEIC at similar visual quality, while offering comparable feature sets including HDR support and alpha transparency, which is why our HEIC to AVIF converter is increasingly popular.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Will I lose quality when converting HEIC to AVIF?</h3>
              <p className="mt-1 text-gray-700">
                When converting from HEIC to AVIF using our converter, there is minimal quality loss as both formats use advanced perceptual compression techniques. In fact, at the same file size, AVIF often provides better visual quality than HEIC.
                Our HEIC to AVIF converter ensures optimal image fidelity through advanced processing algorithms and quality-preserving encoding techniques.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Is it safe to convert HEIC to AVIF online?</h3>
              <p className="mt-1 text-gray-700">
                Yes, our online HEIC to AVIF converter follows strict security protocols when handling all files. Your images are briefly processed on our secure servers and then automatically deleted.
                We don't permanently store your uploaded files or use them for any other purpose. All conversion processes take place in a TLS-encrypted secure environment, ensuring your HEIC to AVIF conversion
                is completely safe and reliable.
              </p>
            </div>
          </div>
        </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 